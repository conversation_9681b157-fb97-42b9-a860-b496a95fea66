"use client"

import { useState } from "react"
import { But<PERSON>, <PERSON>dal, Typo<PERSON> } from "@apollo/ui"
import { InfoCircle } from "@design-systems/apollo-icons"

import { ComponentGroup } from "./common"

export function ModalDemo() {
  const [open, setOpen] = useState(false)
  return (
    <ComponentGroup>
      <Button onClick={() => setOpen(true)}>Open Modal</Button>
      <Modal.Root
        open={open}
        onOpenChange={(open) => {
          setOpen(open)
        }}
        className="max-w-[400px]"
      >
        <Modal.Header icon={<InfoCircle size={20} />}>
          <Typography level="h3">คุณกำลังส่งสร้างโปรโมชั่นใช่หรือไม่ ?คุณกำลังส่งสร้างโปรโมชั่นใช่หรือไม่ ?</Typography>
          <Modal.CloseButton />
        </Modal.Header>
        <Modal.Content>
          <Typography level="body1">
            Once upon a time, there was a forest where plenty of birds lived and
            built their nests on the trees.
          </Typography>
        </Modal.Content>
        <Modal.Footer>
          <Button className="self-stretch" color="negative">
            Button
          </Button>
          <Button className="self-stretch">Button</Button>
        </Modal.Footer>
      </Modal.Root>
    </ComponentGroup>
  )
}
